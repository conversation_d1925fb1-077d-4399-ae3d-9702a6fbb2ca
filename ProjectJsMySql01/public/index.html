<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional User Management Dashboard</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="./index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Meta tags for better SEO and appearance -->
    <meta name="description" content="Professional user management dashboard with real-time statistics and advanced features">
    <meta name="theme-color" content="#2563eb">
</head>
<body>
<div class="dashboard">
    <header class="header">
        <div class="logo">
            <i class="fas fa-chart-line"></i>
            <h1>User Management Pro</h1>
        </div>
        <div class="user-actions">
            <button class="btn btn-primary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i>
                <span>Refresh Data</span>
            </button>
            <button class="btn btn-secondary">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </button>
        </div>
    </header>

    <main class="main-content">
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">Total Users</span>
                    <div class="stat-icon icon-primary">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value" id="totalUsers">0</div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i> 12.4% from last month
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">Active Users</span>
                    <div class="stat-icon icon-success">
                        <i class="fas fa-user-check"></i>
                    </div>
                </div>
                <div class="stat-value" id="activeUsers">0</div>
                <div class="stat-trend">
                    <i class="fas fa-calendar"></i> Last 30 days
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">New Users</span>
                    <div class="stat-icon icon-warning">
                        <i class="fas fa-user-plus"></i>
                    </div>
                </div>
                <div class="stat-value" id="newUsers">0</div>
                <div class="stat-trend">
                    <i class="fas fa-calendar-week"></i> Last 7 days
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">Invalid Data</span>
                    <div class="stat-icon icon-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-value">0</div>
                <div class="stat-trend">
                    <i class="fas fa-database"></i> Data quality issues
                </div>
            </div>
        </div>

        <div class="user-management">
            <div class="section-header">
                <h2 class="section-title">User Management</h2>
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Search users...">
                    <button><i class="fas fa-search"></i></button>
                </div>
            </div>

            <form id="userForm" class="user-form">
                <div class="form-group">
                    <label for="nameInput">Full Name</label>
                    <input type="text" id="nameInput" class="form-control" placeholder="Enter full name">
                </div>
                <div class="form-group">
                    <label for="emailInput">Email Address</label>
                    <input type="email" id="emailInput" class="form-control" placeholder="Enter email address">
                </div>
                <button type="submit" class="submit-btn" id="submitBtn">
                    <i class="fas fa-plus"></i> Add User
                </button>
            </form>

            <div class="table-container">
                <table class="user-table">
                    <thead>
                    <tr>
                        <th class="sortable" onclick="sortTable('id')">
                            ID <i class="fas fa-sort" id="sort-id"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('name')">
                            Name <i class="fas fa-sort" id="sort-name"></i>
                        </th>
                        <th class="sortable" onclick="sortTable('email')">
                            Email <i class="fas fa-sort" id="sort-email"></i>
                        </th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody id="userTableBody">
                    <tr>
                        <td colspan="4" class="text-center py-5">
                            <div class="spinner"></div>
                            <p class="mt-2">Loading users...</p>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination-container">
                <div class="user-count" id="userCount">0 users found</div>
                <ul class="pagination" id="pagination"></ul>
            </div>
        </div>
    </main>

    <footer class="footer">
        <p>&copy; 2023 User Management Dashboard. All rights reserved.</p>
    </footer>
</div>

<!-- Toast Container -->
<div class="toast" id="toastContainer"></div>

<!-- Confirmation Modal -->
<div class="modal" id="confirmModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title" id="modalTitle">Confirm Deletion</h3>
        </div>
        <div class="modal-body" id="modalBody">
            Are you sure you want to delete this user?
        </div>
        <div class="modal-footer">
            <button class="modal-btn modal-btn-cancel" id="cancelBtn">Cancel</button>
            <button class="modal-btn modal-btn-confirm" id="confirmActionBtn">Delete</button>
        </div>
    </div>
</div>

<script src="./index.js"></script>
</body>
</html>