    // Configuration
    const API_BASE_URL = 'http://localhost:3000/api/users';
    const ITEMS_PER_PAGE = 5;
    
    // DOM Elements
    const userTableBody = document.getElementById('userTableBody');
    const userForm = document.getElementById('userForm');
    const searchInput = document.getElementById('searchInput');
    const userCount = document.getElementById('userCount');
    const pagination = document.getElementById('pagination');
    const toastContainer = document.getElementById('toastContainer');
    const confirmModal = document.getElementById('confirmModal');
    const cancelBtn = document.getElementById('cancelBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    
    // State
    let users = [];
    let filteredUsers = [];
    let currentPage = 1;
    let editingId = null;
    let currentSort = { field: 'id', order: 'DESC' };
    
    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
        loadUsers();
        setupEventListeners();
    });
    
    // Set up event listeners
    function setupEventListeners() {
        userForm.addEventListener('submit', (e) => {
            e.preventDefault();
            addUser();
        });
    
        searchInput.addEventListener('input', filterUsers);
    
        cancelBtn.addEventListener('click', () => {
            confirmModal.classList.remove('show');
        });
    
        refreshBtn.addEventListener('click', refreshData);
    }
    
    // API Functions
    async function loadUsers() {
        try {
            showLoading();
            const url = `${API_BASE_URL}?sortBy=${currentSort.field}&sortOrder=${currentSort.order}`;
            const response = await fetch(url);
            if (!response.ok) throw new Error('Failed to fetch users');

            users = await response.json();
            filteredUsers = [...users];
            updateUserCount();
            updateStats();
            updateSortIcons();
            renderUsers();
        } catch (error) {
            showToast('Error loading users', 'danger');
            console.error('Error:', error);
        }
    }
    async function isUserExists(name, email, id) {
        try {
            const response = await fetch(
                `${API_BASE_URL}/check?name=${encodeURIComponent(name)}&email=${encodeURIComponent(email)}&excludeId=${id || ''}`
            );

            if (!response.ok) {
                throw new Error('Validation check failed');
            }

            const data = await response.json();
            return data.exists;
        } catch (error) {
            console.error('Validation error:', error);
            return false; // fail-safe for production, but maybe use false during dev
        }
    }
    
    
    
    async function addUser() {
        const name = document.getElementById('nameInput').value.trim();
        const email = document.getElementById('emailInput').value.trim();
    
        if (!name || !email) {
            showToast('Please fill all fields', 'warning');
            return;
        }
        // Check if user exists
        const userExists = await isUserExists(name, email);
        if (userExists) {
            showToast('User with this name or email already exists', 'danger');
            return;
        }
    
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner"></span> Adding...';
    
        try {
            const response = await fetch(API_BASE_URL, {
                method: 'POST', headers: {'Content-Type': 'application/json'}, body: JSON.stringify({name, email})
            });
    
            if (!response.ok) throw new Error('Failed to add user');
    
            userForm.reset();
            showToast('User added successfully', 'success');
            await loadUsers();
        } catch (error) {
            showToast('Error adding user', 'danger');
            console.error('Error:', error);
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-plus"></i> Add User';
        }
    }
    
    async function updateUser(id, name, email) {
        try {
            const response = await fetch(`${API_BASE_URL}/${id}`, {
                method: 'PUT', headers: {'Content-Type': 'application/json'}, body: JSON.stringify({name, email})
            });
    
            if (!response.ok) throw new Error('Failed to update user');
    
            showToast('User updated successfully', 'success');
            await loadUsers();
        } catch (error) {
            showToast('Error updating user', 'danger');
            console.error('Error:', error);
        }
    }
    
    async function deleteUser(id) {
        try {
            const response = await fetch(`${API_BASE_URL}/${id}`, {
                method: 'DELETE'
            });
    
            if (!response.ok) throw new Error('Failed to delete user');
    
            showToast('User deleted successfully', 'success');
            await loadUsers();
        } catch (error) {
            showToast('Error deleting user', 'danger');
            console.error('Error:', error);
        }
    }
    
    // UI Functions
    function renderUsers() {
        if (filteredUsers.length === 0) {
            userTableBody.innerHTML = `
                        <tr>
                            <td colspan="4" class="text-center py-5 text-muted">
                                <i class="fas fa-user-slash fa-2x mb-3"></i>
                                <p>No users found</p>
                            </td>
                        </tr>
                    `;
            return;
        }
    
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const paginatedUsers = filteredUsers.slice(startIndex, startIndex + ITEMS_PER_PAGE);
    
        userTableBody.innerHTML = paginatedUsers.map(user => `
                    <tr>
                        <td>${user.id}</td>
                        <td>
                            <input type="text"
                                   class="form-control form-control-sm editable-input ${editingId === user.id ? 'editing' : ''}"
                                   id="name-${user.id}"
                                   value="${user.name || ''}"
                                   ${editingId !== user.id ? 'readonly' : ''}>
                        </td>
                        <td>
                            <input type="email"
                                   class="form-control form-control-sm editable-input ${editingId === user.id ? 'editing' : ''}"
                                   id="email-${user.id}"
                                   value="${user.email || ''}"
                                   ${editingId !== user.id ? 'readonly' : ''}>
                        </td>
                        <td class="action-buttons">
                            ${editingId !== user.id ? `<button class="action-btn edit-btn" onclick="startEditing(${user.id})">
                                    <i class="fas fa-edit"></i>
                                </button>` : `<button class="action-btn save-btn" onclick="saveChanges(${user.id})">
                                    <i class="fas fa-check"></i>
                                </button>`}
                            <button class="action-btn delete-btn" onclick="confirmDelete(${user.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
    
        renderPagination();
    }
    
    function renderPagination() {
        const totalPages = Math.ceil(filteredUsers.length / ITEMS_PER_PAGE);
    
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
    
        let paginationHTML = '';
    
        // Previous button
        paginationHTML += `
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                `;
    
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            paginationHTML += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                        </li>
                    `;
        }
    
        // Next button
        paginationHTML += `
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                `;
    
        pagination.innerHTML = paginationHTML;
    }
    
    function showLoading() {
        userTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center py-5">
                            <div class="spinner"></div>
                            <p class="mt-2">Loading users...</p>
                        </td>
                    </tr>
                `;
    }
    
    function showToast(message, type = 'info') {
        toastContainer.textContent = message;
        toastContainer.className = `toast show toast-${type}`;
    
        setTimeout(() => {
            toastContainer.classList.add('hide');
            setTimeout(() => {
                toastContainer.classList.remove('show', 'hide');
            }, 300);
        }, 3000);
    }
    
    // User Actions
    function startEditing(id) {
        editingId = id;
        renderUsers();
    }
    
    function cancelEditing() {
        editingId = null;
        renderUsers();
    }
    
    async function saveChanges(id) {
        const name = document.getElementById(`name-${id}`).value.trim();
        const email = document.getElementById(`email-${id}`).value.trim();
    
        if (!name || !email) {
            showToast('Please fill all fields', 'warning');
            return;
        }
    
        // Check if user exists (excluding current user)
        const userExists = await isUserExists(name, email, id);
    
        if (userExists) {
            showToast('User with this name or email already exists', 'danger');
            return;
        }
    
        await updateUser(id, name, email);
        editingId = null;
    }
    
    function confirmDelete(id) {
        const user = users.find(u => u.id === id);
        document.getElementById('modalTitle').textContent = 'Confirm Deletion';
        document.getElementById('modalBody').innerHTML = `
                    Are you sure you want to delete <strong>${user.name}</strong> (${user.email})?
                    <br><small class="text-muted">This action cannot be undone.</small>
                `;
    
        const confirmBtn = document.getElementById('confirmActionBtn');
        confirmBtn.onclick = () => {
            confirmModal.classList.remove('show');
            deleteUser(id);
        };
    
        confirmModal.classList.add('show');
    }
    
    // Utility Functions
    function filterUsers() {
        const searchTerm = searchInput.value.toLowerCase();
    
        if (!searchTerm) {
            filteredUsers = [...users];
        } else {
            filteredUsers = users.filter(user => (user.name && user.name.toLowerCase().includes(searchTerm)) || (user.email && user.email.toLowerCase().includes(searchTerm)));
        }
    
        currentPage = 1;
        updateUserCount();
        renderUsers();
    }
    
    function changePage(page) {
        if (page < 1 || page > Math.ceil(filteredUsers.length / ITEMS_PER_PAGE)) return;
        currentPage = page;
        renderUsers();
    }
    
    function updateUserCount() {
        const count = filteredUsers.length;
        userCount.textContent = `${count} user${count !== 1 ? 's' : ''} found`;
    }
    
    async function updateStats() {
        try {
            const response = await fetch(`${API_BASE_URL}/stats`);
            if (!response.ok) throw new Error('Failed to fetch statistics');

            const stats = await response.json();

            // Update the statistics display
            document.getElementById('totalUsers').textContent = stats.totalUsers;
            document.getElementById('activeUsers').textContent = stats.activeUsers;
            document.getElementById('newUsers').textContent = stats.newUsers;

            // Update the pending actions (4th stat card)
            const pendingElement = document.querySelector('.stat-card:nth-child(4) .stat-value');
            if (pendingElement) {
                pendingElement.textContent = stats.pendingActions;
            }

        } catch (error) {
            console.error('Error fetching statistics:', error);
            // Fallback to old calculation if API fails
            document.getElementById('totalUsers').textContent = users.length;
            document.getElementById('activeUsers').textContent = Math.floor(users.length * 0.8);
            document.getElementById('newUsers').textContent = Math.floor(users.length * 0.2);
        }
    }
    
    function refreshData() {
        currentPage = 1;
        searchInput.value = '';
        loadUsers();
    }

    // Sorting Functions
    function sortTable(field) {
        // Toggle sort order if clicking the same field
        if (currentSort.field === field) {
            currentSort.order = currentSort.order === 'ASC' ? 'DESC' : 'ASC';
        } else {
            currentSort.field = field;
            currentSort.order = 'ASC';
        }

        currentPage = 1;
        loadUsers();
    }

    function updateSortIcons() {
        // Reset all sort icons
        document.querySelectorAll('.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        // Update the active sort icon
        const activeIcon = document.getElementById(`sort-${currentSort.field}`);
        if (activeIcon) {
            if (currentSort.order === 'ASC') {
                activeIcon.className = 'fas fa-sort-up';
            } else {
                activeIcon.className = 'fas fa-sort-down';
            }
        }
    }