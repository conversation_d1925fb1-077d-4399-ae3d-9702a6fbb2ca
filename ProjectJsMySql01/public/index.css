:root {
    /* Professional Color Palette */
    --primary: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary: #64748b;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #1e293b;

    /* Card & Component Colors */
    --card-bg: #ffffff;
    --card-border: #e2e8f0;
    --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --card-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --card-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-light: #94a3b8;

    /* Transitions */
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    padding: 1.5rem;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    color: var(--text-primary);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* Main Dashboard Container */
.dashboard {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    background: var(--card-bg);
    border-radius: var(--radius-2xl);
    box-shadow: var(--card-shadow-xl);
    border: 1px solid var(--card-border);
    overflow: hidden;
    min-height: calc(100vh - 3rem);
}

/* Professional Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2.5rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: white;
    position: relative;
    border-bottom: 1px solid var(--card-border);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 2;
    position: relative;
}

.logo i {
    font-size: 2rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.user-actions {
    display: flex;
    gap: 1rem;
    z-index: 2;
    position: relative;
}

/* Professional Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius);
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    outline: none;
    position: relative;
}

.btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-color: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    padding: 2.5rem;
    background: var(--bg-secondary);
}

/* Professional Stats Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--card-border);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-lg);
    border-color: var(--primary);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.stat-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.icon-primary {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.icon-success {
    background: linear-gradient(135deg, var(--success), #34d399);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.icon-warning {
    background: linear-gradient(135deg, var(--warning), #fbbf24);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.icon-danger {
    background: linear-gradient(135deg, var(--danger), #f87171);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1;
}

.stat-trend {
    font-size: 0.875rem;
    color: var(--text-tertiary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.stat-trend i {
    font-size: 0.75rem;
}

.trend-down {
    color: var(--danger);
}

/* User Management Section */
.user-management {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--card-border);
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--card-border);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-title::before {
    content: '';
    width: 4px;
    height: 1.5rem;
    background: linear-gradient(135deg, var(--primary), var(--accent));
    border-radius: 2px;
}

/* Professional Search Bar */
.search-bar {
    display: flex;
    background: var(--card-bg);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--card-border);
    transition: var(--transition);
}

.search-bar:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-bar input {
    padding: 0.75rem 1rem;
    border: none;
    width: 300px;
    font-size: 0.875rem;
    background: transparent;
    color: var(--text-primary);
    font-family: inherit;
}

.search-bar input:focus {
    outline: none;
}

.search-bar input::placeholder {
    color: var(--text-tertiary);
}

.search-bar button {
    background: var(--primary);
    color: white;
    border: none;
    padding: 0 1.25rem;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.search-bar button:hover {
    background: var(--primary-dark);
}

/* Professional User Form */
.user-form {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 1.5rem;
    align-items: end;
    padding: 1.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--card-border);
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin: 0;
}

.form-control {
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    border: 1px solid var(--card-border);
    font-size: 0.875rem;
    transition: var(--transition);
    background: var(--card-bg);
    color: var(--text-primary);
    font-family: inherit;
    width: 100%;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control::placeholder {
    color: var(--text-tertiary);
}

/* Professional Submit Button */
.submit-btn {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-family: inherit;
    white-space: nowrap;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.submit-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--card-shadow-lg);
}

.submit-btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Enhanced Submit Button States */
.submit-btn.loading {
    background: var(--gray-400);
    color: white;
    pointer-events: none;
}

.submit-btn.success {
    background: var(--success);
    color: white;
    transform: scale(1.02);
}

.submit-btn.error {
    background: var(--danger);
    color: white;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Button Icon Animations */
.submit-btn i {
    transition: var(--transition);
}

.submit-btn:hover i {
    transform: scale(1.1);
}

.submit-btn.loading .spinner {
    animation: spin 1s linear infinite;
}

/* User Table */
.user-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-bg);
    border-radius: 14px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.user-table th {
    background: var(--gray-100);
    padding: 1rem 1.25rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.875rem;
    border-bottom: 1px solid var(--card-border);
}

/* Sortable Table Headers */
.sortable {
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
    position: relative;
}

.sortable:hover {
    background-color: var(--gray-100);
    color: var(--primary);
}

.sortable i {
    margin-left: 8px;
    opacity: 0.6;
    transition: var(--transition);
    font-size: 12px;
}

.sortable:hover i {
    opacity: 1;
}

.sortable i.fa-sort-up,
.sortable i.fa-sort-down {
    opacity: 1;
    color: var(--primary);
}

.user-table td {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--card-border);
    color: var(--text-primary);
    vertical-align: middle;
}

.user-table tr:last-child td {
    border-bottom: none;
}

.user-table tr:hover {
    background: var(--gray-50);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    justify-content: center;
}

.action-btn {
    width: 2.25rem;
    height: 2.25rem;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.edit-btn {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.edit-btn:hover {
    background: rgba(59, 130, 246, 0.2);
}

.delete-btn {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.delete-btn:hover {
    background: rgba(239, 68, 68, 0.2);
}

.save-btn {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.save-btn:hover {
    background: rgba(34, 197, 94, 0.2);
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.status-active {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success);
}

.status-inactive {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    width: 100%; /* Ensure full width */
    padding: 0 10px; /* Add padding to match table */
}

.pagination {
    display: flex;
    gap: 8px;
}

.page-item {
    list-style: none;
}

.page-link {
    display: block;
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    background: var(--card-bg);
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid var(--card-border);
}

.page-link:hover {
    background: var(--primary);
}

.page-item.active .page-link {
    background: var(--primary);
    border-color: var(--primary);
}

.page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

.user-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Footer */
.footer {
    padding: 1.25rem 1.875rem;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
    border-top: 1px solid var(--card-border);
    background: var(--bg-secondary);
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast */
.toast {
    position: fixed;
    top: 1.25rem;
    right: 1.25rem;
    padding: 1rem 1.25rem;
    border-radius: var(--radius-lg);
    color: white;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-20px);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: var(--card-shadow-lg);
    font-weight: 500;
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast.hide {
    opacity: 0;
    transform: translateY(-20px);
}

.toast-success {
    background: var(--success);
}

.toast-danger {
    background: var(--danger);
}

.toast-warning {
    background: var(--warning);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--card-shadow-xl);
    border: 1px solid var(--card-border);
    transform: translateY(-20px);
    transition: var(--transition);
}

.modal.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    margin-bottom: 20px;
}

.modal-title {
    font-size: 1.375rem;
    color: var(--text-primary);
    font-weight: 600;
}

.modal-body {
    margin-bottom: 2rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.modal-btn {
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-size: 0.875rem;
}

.modal-btn-cancel {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--card-border);
}

.modal-btn-cancel:hover {
    background: var(--gray-100);
}

.modal-btn-confirm {
    background: var(--danger);
    color: white;
}

.modal-btn-confirm:hover {
    background: #e11d48;
}

/* Responsive Design */
@media (max-width: 992px) {
    .user-form {
        grid-template-columns: 1fr;
    }

    .stats-cards {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .user-actions {
        width: 100%;
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .search-bar {
        width: 100%;
    }

    .search-bar input {
        width: 100%;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .user-table {
        display: block;
        overflow-x: auto;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }
}