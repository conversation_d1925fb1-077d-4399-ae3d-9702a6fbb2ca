:root {
    /* Professional Color Palette */
    --primary: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary: #64748b;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #1e293b;

    /* Card & Component Colors */
    --card-bg: #ffffff;
    --card-border: #e2e8f0;
    --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --card-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --card-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-light: #94a3b8;

    /* Transitions */
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    padding: 1.5rem;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    color: var(--text-primary);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* Main Dashboard Container */
.dashboard {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    background: var(--card-bg);
    border-radius: var(--radius-2xl);
    box-shadow: var(--card-shadow-xl);
    border: 1px solid var(--card-border);
    overflow: hidden;
    min-height: calc(100vh - 3rem);
}

/* Professional Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2.5rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: white;
    position: relative;
    border-bottom: 1px solid var(--card-border);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 2;
    position: relative;
}

.logo i {
    font-size: 2rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.user-actions {
    display: flex;
    gap: 1rem;
    z-index: 2;
    position: relative;
}

/* Professional Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius);
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    outline: none;
    position: relative;
}

.btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-color: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    padding: 2.5rem;
    background: var(--bg-secondary);
}

/* Professional Stats Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--card-border);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-lg);
    border-color: var(--primary);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.stat-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.icon-primary {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.icon-success {
    background: linear-gradient(135deg, var(--success), #34d399);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.icon-warning {
    background: linear-gradient(135deg, var(--warning), #fbbf24);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.icon-danger {
    background: linear-gradient(135deg, var(--danger), #f87171);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1;
}

.stat-trend {
    font-size: 0.875rem;
    color: var(--text-tertiary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.stat-trend i {
    font-size: 0.75rem;
}

.trend-down {
    color: var(--danger);
}

/* User Management Section */
.user-management {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--card-border);
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.section-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--light);
}

.search-bar {
    display: flex;
    background: var(--darker);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--glass-border);
}

.search-bar input {
    padding: 12px 20px;
    border: none;
    width: 300px;
    font-size: 15px;
    background: transparent;
    color: var(--light);
}

.search-bar input:focus {
    outline: none;
}

.search-bar input::placeholder {
    color: var(--gray);
}

.search-bar button {
    background: var(--primary);
    color: white;
    border: none;
    padding: 0 20px;
    cursor: pointer;
    transition: var(--transition);
}

.search-bar button:hover {
    background: var(--primary-dark);
}

/* User Form */
.user-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /*grid-template-columns: 1fr 1fr auto;*/
    gap: 15px;
    padding: 5px;
    margin-bottom: 25px;
}

.form-group {
    display: flex;
    width: 50%;

    align-items: center;
    flex-direction: row;
}

.form-group label {
    font-size: 14px;
    margin-right: 10px;
    color: var(--gray);
    margin-bottom: 8px;
    font-weight: 500;
}
.form-group input {
    width: 100%;
}

.form-control {
    padding: 14px 16px;
    border-radius: 12px;
    border: 1px solid var(--glass-border);
    font-size: 15px;
    transition: var(--transition);
    background: var(--darker);
    color: var(--light);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
}

.submit-btn {
    background: var(--primary);
    height: 50px;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0 20px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 15px;
}

.submit-btn:hover {
    background: teal;
    transform: translateY(-2px);
}

/* User Table */
.user-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-bg);
    border-radius: 14px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.user-table th {
    background: var(--darker);
    padding: 16px 20px;
    text-align: left;
    font-weight: 600;
    color: var(--gray);
    font-size: 14px;
}

/* Sortable Table Headers */
.sortable {
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
    position: relative;
}

.sortable:hover {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary);
}

.sortable i {
    margin-left: 8px;
    opacity: 0.6;
    transition: var(--transition);
    font-size: 12px;
}

.sortable:hover i {
    opacity: 1;
}

.sortable i.fa-sort-up,
.sortable i.fa-sort-down {
    opacity: 1;
    color: var(--primary);
}

.user-table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--glass-border);
    color: var(--light);
    vertical-align: middle; /* Fix vertical alignment */
}

.user-table tr:last-child td {
    border-bottom: none;
}

.user-table tr:hover {
    background: rgba(30, 41, 59, 0.6);
}

.action-buttons {
    display: flex;
    gap: 10px;
    align-items: center; /* Center buttons vertically */
    justify-content: center;
}

.action-btn {
    width: 36px;
    height: 52px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.edit-btn {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.edit-btn:hover {
    background: rgba(59, 130, 246, 0.2);
}

.delete-btn {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.delete-btn:hover {
    background: rgba(239, 68, 68, 0.2);
}

.save-btn {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.save-btn:hover {
    background: rgba(34, 197, 94, 0.2);
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.status-active {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success);
}

.status-inactive {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    width: 100%; /* Ensure full width */
    padding: 0 10px; /* Add padding to match table */
}

.pagination {
    display: flex;
    gap: 8px;
}

.page-item {
    list-style: none;
}

.page-link {
    display: block;
    padding: 8px 16px;
    border-radius: 8px;
    background: var(--darker);
    color: var(--light);
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid var(--glass-border);
}

.page-link:hover {
    background: var(--primary);
}

.page-item.active .page-link {
    background: var(--primary);
    border-color: var(--primary);
}

.page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

.user-count {
    font-size: 14px;
    color: var(--gray);
}

/* Footer */
.footer {
    padding: 20px 30px;
    text-align: center;
    color: var(--gray);
    font-size: 14px;
    border-top: 1px solid var(--glass-border);
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 12px;
    color: white;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast.hide {
    opacity: 0;
    transform: translateY(-20px);
}

.toast-success {
    background: var(--success);
}

.toast-danger {
    background: var(--danger);
}

.toast-warning {
    background: var(--warning);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--darker);
    border-radius: 16px;
    padding: 30px;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow);
    border: 1px solid var(--glass-border);
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

.modal.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    margin-bottom: 20px;
}

.modal-title {
    font-size: 22px;
    color: var(--light);
    font-weight: 700;
}

.modal-body {
    margin-bottom: 30px;
    color: var(--gray);
    line-height: 1.6;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.modal-btn {
    padding: 10px 20px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.modal-btn-cancel {
    background: transparent;
    color: var(--gray);
    border: 1px solid var(--glass-border);
}

.modal-btn-cancel:hover {
    background: rgba(148, 163, 184, 0.1);
}

.modal-btn-confirm {
    background: var(--danger);
    color: white;
}

.modal-btn-confirm:hover {
    background: #e11d48;
}

/* Responsive Design */
@media (max-width: 992px) {
    .user-form {
        grid-template-columns: 1fr;
    }

    .stats-cards {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .user-actions {
        width: 100%;
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .search-bar {
        width: 100%;
    }

    .search-bar input {
        width: 100%;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .user-table {
        display: block;
        overflow-x: auto;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }
}