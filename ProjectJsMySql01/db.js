const mysql = require('mysql2');

// Create connection with promise support
const db = mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'gestion_utilisateurs',
}).promise();

// Test connection
(async () => {
    try {
        await db.execute('SELECT 1');
        console.log('Connected to database successfully');
    } catch (err) {
        console.error('Error connecting to database:', err);
        process.exit(1);
    }
})();

module.exports = db;
