const express = require('express');
const app = express();
const cors = require('cors');
const usersRouter = require('./routes/userRoute');
const { initializeDatabase } = require('./init-db');

app.use(cors());
app.use(express.json());
app.use('/api/users', usersRouter);
app.use(express.static('public'));

// Initialize database and start server
async function startServer() {
    try {
        // Initialize database schema
        await initializeDatabase();

        // Start the server
        app.listen(3000, () => {
            console.log('Server is running on port 3000');
            console.log('Database schema initialized successfully');
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();